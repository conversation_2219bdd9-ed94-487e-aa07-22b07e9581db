# 购物车接口优化说明

## 修改内容

根据协议文档 `apidoc\proto\mall\inquiry\cart.proto`，以下接口都会直接返回 `MidCartByTabResp` 类型的购物车数据：

- `updateCart` - 修改购物车商品
- `removeCart` - 删除购物车商品
- `updateSelectedAll` - 更新全部选择或全部不选中

因此，在这些接口调用成功后，不需要再单独调用 `getCartByTab` 接口获取购物车数据。

## 修改的文件

1. `src\pages\find\index.vue` (PC 版本)
2. `src\pages\h5\find\index.vue` (H5 版本)

## 具体修改

### 1. 移除不必要的 `onGetCartByTab()` 调用

在以下函数中移除了 `onGetCartByTab()` 调用：

- `onAllSelection()` - 全选/全不选
- `onUpdateCart()` - 修改购物车
- `onRemoveCart()` - 删除购物车
- `onConfirm()` - 规格确认

### 2. 重构数据处理逻辑，消除代码重复

#### 问题发现

原来的 `onGetCartByTab()` 函数和新增的 `updateCartDataFromResponse()` 函数中存在大量重复的数据处理逻辑。

#### 解决方案

创建了通用的 `processCartData()` 函数来统一处理购物车数据：

```javascript
// 通用的购物车数据处理函数
function processCartData(data: any, isInit: boolean = false) {
  if (!data) return;

  // 设置Tab数据
  pageData.onlineOrderCartTab = data?.onlineOrderCartTab || null;
  pageData.goodsLookingCartTab = data?.goodsLookingCartTab || null;

  // 确定显示模式和默认Tab
  const hasDirectPayment = !!pageData.onlineOrderCartTab;
  const hasQuotation = !!pageData.goodsLookingCartTab;

  // 保存当前的activeTab状态
  const currentActiveTab = pageData.activeTab;

  if (hasDirectPayment && hasQuotation) {
    // 两种商品都有，显示Tab
    pageData.showTabs = true;

    // 只在初始化时设置默认tab，其他时候保持当前tab
    if (isInit || !currentActiveTab) {
      pageData.activeTab = "direct";
    }

    // 根据当前activeTab设置对应的数据
    if (pageData.activeTab === "direct") {
      pageData.currentCartData = pageData.onlineOrderCartTab;
    } else {
      pageData.currentCartData = pageData.goodsLookingCartTab;
    }
  } else if (hasDirectPayment) {
    // 只有直接付款商品
    pageData.showTabs = false;
    pageData.activeTab = "direct";
    pageData.currentCartData = pageData.onlineOrderCartTab;
  } else if (hasQuotation) {
    // 只有询价商品
    pageData.showTabs = false;
    pageData.activeTab = "quotation";
    pageData.currentCartData = pageData.goodsLookingCartTab;
  }

  // 处理商品数据
  if (pageData.currentCartData?.goodsList) {
    processGoodsData(pageData.currentCartData.goodsList);
  }

  // H5版本还需要更新兼容字段
  // pageData.goodsList = pageData.currentCartData?.goodsList || [];
  // pageData.stat = pageData.currentCartData?.stat || {};
  // pageData.totalEstimateFreight = pageData.currentCartData?.totalEstimateFreight || null;

  // 同步更新store的购物车数据
  authStore.getCartList(data);
}

// 使用接口返回的数据更新购物车状态
function updateCartDataFromResponse(data: any) {
  processCartData(data, false);
}
```

#### 函数调用优化

- `onGetCartByTab()`: 调用 `processCartData(data, type === "init")`
- `updateCartDataFromResponse()`: 调用 `processCartData(data, false)`

## 优化效果

1. **减少网络请求**：避免了 4 个不必要的接口调用
2. **提升响应速度**：直接使用返回数据更新页面状态
3. **保持数据一致性**：确保页面数据与服务端完全同步
4. **消除代码重复**：通过 `processCartData()` 统一处理数据逻辑
5. **提高代码可维护性**：数据处理逻辑集中管理，便于后续维护和修改
6. **增强代码复用性**：同一套逻辑适用于初始化和更新场景

## 注意事项

- 初始化时仍需要调用 `onGetCartByTab("init")` 获取购物车数据
- `onGetCartByTab` 函数本身保留，用于初始化和其他需要主动获取数据的场景
- 所有修改都保持了原有的错误处理逻辑
