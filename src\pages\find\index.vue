<template>
  <div
    class="w-full min-h-[90vh]"
    :class="pageData.showTabs ? 'bg-[#f2f2f2]' : 'bg-[#fff]'"
  >
    <div class="bg-white">
      <search-card></search-card>
    </div>

    <div
      class="cwidth flex mx-auto"
      :class="pageData.showTabs ? 'px-[8px]' : ''"
    >
      <div class="flex-1 h-fit-content">
        <!-- Tab区域容器（包含切换和内容，带背景图） -->
        <div v-if="pageData.showTabs" class="relative mt-[4px]">
          <!-- Tab背景图 -->
          <div class="absolute inset-0 pointer-events-none z-0">
            <!-- 直接付款商品Tab背景 -->
            <img
              v-if="pageData.activeTab === 'direct'"
              class="w-[1264px] min-h-[838px]"
              src="@/assets/icons/find/payment-bg.svg"
            />
            <!-- 询价商品Tab背景 -->
            <img
              v-if="pageData.activeTab === 'quotation'"
              class="w-[1264px] min-h-[676px]"
              src="@/assets/icons/find/quotation-bg.svg"
            />
          </div>

          <!-- Tab内容容器 -->
          <div class="relative z-10">
            <!-- Tab 切换 -->
            <div>
              <div class="flex">
                <div
                  v-if="pageData.onlineOrderCartTab"
                  @click="switchTab('direct')"
                  :class="[
                    'w-[240px] flex items-center justify-center px-[14px] py-[13px] cursor-pointer gap-[6px] transition-all duration-300 rounded-[8px] m-[4px]',
                    pageData.activeTab === 'direct'
                      ? 'bg-[#e4f2e5] '
                      : 'bg-[#FFF] ',
                  ]"
                >
                  <img
                    src="@/assets/icons/find/payment.svg"
                    alt="payment"
                    class="w-[24px] h-[24px]"
                    referrerpolicy="no-referrer"
                  />
                  <span class="text-[16px] leading-[16px]">{{
                    authStore.i18n("cm_find.directPaymentGoods")
                  }}</span>
                  <span
                    class="text-[16px] leading-[16px] font-medium text-[#1EA62A]"
                    >({{ pageData.onlineOrderCartTab.stat?.skuCount }})</span
                  >
                </div>
                <div
                  v-if="pageData.goodsLookingCartTab"
                  @click="switchTab('quotation')"
                  :class="[
                    'w-[240px] flex items-center justify-center px-[14px] py-[13px] cursor-pointer gap-[6px] transition-all duration-300 rounded-[8px] m-[4px] ',
                    pageData.activeTab === 'quotation'
                      ? 'bg-[#E3EBF2] '
                      : 'bg-[#FFF]  ',
                  ]"
                >
                  <img
                    src="@/assets/icons/find/quotation.svg"
                    alt="quotation"
                    class="w-[24px] h-[24px]"
                    referrerpolicy="no-referrer"
                  />
                  <span class="text-[16px] leading-[16px]">{{
                    authStore.i18n("cm_find.inquiryGoods")
                  }}</span>
                  <span
                    class="text-[16px] leading-[16px] font-medium text-[#1B80E4]"
                    >({{
                      pageData.goodsLookingCartTab.stat?.skuCount || 0
                    }})</span
                  >
                </div>
              </div>
            </div>

            <!-- Tab 内容 -->
            <div>
              <!-- 直接付款商品Tab -->
              <DirectPaymentTab
                v-if="
                  pageData.activeTab === 'direct' && pageData.onlineOrderCartTab
                "
                :cartData="pageData.onlineOrderCartTab"
                @onAllSelection="onAllSelection"
                @onGoodsSelection="onGoodsSelection"
                @onSkuSelection="onSkuSelection"
                @onCartQtyUpdate="onCartQtyUpdate"
                @onDeleteGoods="onDeleteGoods"
                @onDeleteSku="onDeleteSku"
                @onOpenSkuDialog="onOpenSkuDialog"
                @onGoLoginRegister="onGoLoginRegister"
                :showTabs="pageData.showTabs"
              />

              <!-- 询价商品Tab -->
              <QuotationRequiredTab
                v-if="
                  pageData.activeTab === 'quotation' &&
                  pageData.goodsLookingCartTab
                "
                :cartData="pageData.goodsLookingCartTab"
                @onAllSelection="onAllSelection"
                @onGoodsSelection="onGoodsSelection"
                @onSkuSelection="onSkuSelection"
                @onCartQtyUpdate="onCartQtyUpdate"
                @onDeleteGoods="onDeleteGoods"
                @onDeleteSku="onDeleteSku"
                @onOpenSkuDialog="onOpenSkuDialog"
                @onGoLoginRegister="onGoLoginRegister"
                :showTabs="pageData.showTabs"
              />

              <!-- 空状态 -->
              <n-empty
                v-if="!pageData.currentCartData?.goodsList?.length"
                :description="authStore.i18n('cm_find.noData')"
                class="mt-24"
              >
                <template #extra>
                  <n-button
                    size="small"
                    color="#E50113"
                    text-color="#fff"
                    @click="onGoHome"
                  >
                    {{ authStore.i18n("cm_find.goHome") }}</n-button
                  >
                </template>
              </n-empty>
            </div>
          </div>
        </div>

        <!-- 无Tab时的内容 -->
        <div v-if="!pageData.showTabs && !pageData.loading">
          <!-- 直接付款商品 -->
          <DirectPaymentTab
            v-if="pageData.onlineOrderCartTab"
            :cartData="pageData.onlineOrderCartTab"
            @onAllSelection="onAllSelection"
            @onGoodsSelection="onGoodsSelection"
            @onSkuSelection="onSkuSelection"
            @onCartQtyUpdate="onCartQtyUpdate"
            @onDeleteGoods="onDeleteGoods"
            @onDeleteSku="onDeleteSku"
            @onOpenSkuDialog="onOpenSkuDialog"
            @onGoLoginRegister="onGoLoginRegister"
          />

          <!-- 询价商品 -->
          <QuotationRequiredTab
            v-if="pageData.goodsLookingCartTab"
            :cartData="pageData.goodsLookingCartTab"
            @onAllSelection="onAllSelection"
            @onGoodsSelection="onGoodsSelection"
            @onSkuSelection="onSkuSelection"
            @onCartQtyUpdate="onCartQtyUpdate"
            @onDeleteGoods="onDeleteGoods"
            @onDeleteSku="onDeleteSku"
            @onOpenSkuDialog="onOpenSkuDialog"
            @onGoLoginRegister="onGoLoginRegister"
          />

          <!-- 空状态 -->
          <n-empty
            v-if="!pageData.currentCartData?.goodsList?.length"
            :description="authStore.i18n('cm_find.noData')"
            class="mt-24"
          >
            <template #extra>
              <n-button
                size="small"
                color="#E50113"
                text-color="#fff"
                @click="onGoHome"
              >
                {{ authStore.i18n("cm_find.goHome") }}</n-button
              >
            </template>
          </n-empty>
        </div>
        <div v-show="pageData.loading" class="loading-overlay">
          <n-spin stroke="#e50113" :show="pageData.loading"> </n-spin>
        </div>
      </div>
    </div>
    <!-- 选择商品规格 -->
    <n-modal
      preset="dialog"
      :show-icon="false"
      :closable="false"
      :style="{ width: '600px' }"
      v-model:show="pageData.dialogVisible"
    >
      <!-- 商品规格 -->
      <div>
        <div
          v-for="spec in pageData.currentGoods.specList"
          :key="spec.id"
          class="ml-2 mt-3"
        >
          <div class="mb-4">{{ spec.name }}:</div>
          <div class="flex flex-wrap mb-4">
            <div
              v-for="item in spec.items"
              :key="item.itemId"
              @click="onSpecUpdate(spec.id, item.itemId, item)"
              class="spec-btn min-w-10 max-w-4/5 relative"
              :class="{
                'current-btn': pageData.selectedSpec[spec?.id] == item.itemId,
                'disabled-btn': item.disabled,
              }"
            >
              <n-image
                lazy
                preview-disabled
                object-fit="fill"
                class="w-[36px] h-[36px] shrink-0 mr-3"
                :src="item.imageUrl"
                v-if="item.imageUrl"
                :img-props="{ referrerpolicy: 'no-referrer' }"
              />
              <span class="py-2">{{ item.itemName }}</span>
              <icon-card
                size="20"
                name="typcn:tick"
                color="#fff"
                class="btn-tick mr-2"
              ></icon-card>
            </div>
          </div>
        </div>
        <n-divider />
        <div>
          <n-button
            @click="onConfirm"
            color="#E50113"
            class="mr-4"
            :disabled="!pageData.updatedSku"
            text-color="#fff"
            >{{ authStore.i18n("cm_find.confirm") }}</n-button
          >
          <n-button @click="onCancel">{{
            authStore.i18n("cm_find.cancel")
          }}</n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

import DirectPaymentTab from "@/pages/find/components/DirectPaymentTab.vue";
import QuotationRequiredTab from "@/pages/find/components/QuotationRequiredTab.vue";

const message = useMessage();
const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");

const pageData = reactive<any>({
  // Tab相关
  activeTab: "direct", // 'direct' | 'quotation'
  showTabs: false,

  // 购物车数据
  onlineOrderCartTab: null, // 直接付款商品Tab数据
  goodsLookingCartTab: null, // 询价商品Tab数据
  currentCartData: null, // 当前显示的购物车数据

  // 弹窗状态
  dialogVisible: false,

  // 商品规格选择
  currentSku: <any>{},
  updatedSku: <any>{},
  currentGoods: <any>{},
  selectedSpec: <any>{},

  // 加载状态
  loading: false,
});

watch(
  () => pageData.selectedSpec,
  (newVal: any) => {
    // 多组规格时 选中倒数第二组的时候 需要检验sku的库存以及上下架状态
    if (
      newVal &&
      pageData.currentGoods?.specList?.length > 1 &&
      Object.values(newVal).length > pageData.currentGoods?.specList.length - 2
    ) {
      const targetId =
        pageData.currentGoods?.specList[
          pageData.currentGoods?.specList?.length - 2
        ].id;
      for (const id in pageData.selectedSpec) {
        if (targetId == id) {
          updateSpecStatus();
          break;
        }
      }
    } else {
      //只有一组时 直接校验sku的库存以及上下架状态
      updateOneSpecStatus();
    }
  },
  { deep: true }
);

// 初始化页面数据
onGetCartByTab("init");

// 获取购物车列表（按Tab分割）
async function onGetCartByTab(type?: string) {
  if (type === "init") {
    pageData.loading = true;
  }
  const res: any = await useGetCartByTab({});
  if (type === "init") {
    pageData.loading = false;
  }

  if (res?.result?.code === 200) {
    const data = res?.data;

    // 设置Tab数据
    pageData.onlineOrderCartTab = data?.onlineOrderCartTab || null;
    pageData.goodsLookingCartTab = data?.goodsLookingCartTab || null;

    // 确定显示模式和默认Tab
    const hasDirectPayment = !!pageData.onlineOrderCartTab;
    const hasQuotation = !!pageData.goodsLookingCartTab;

    // 保存当前的activeTab状态
    const currentActiveTab = pageData.activeTab;

    if (hasDirectPayment && hasQuotation) {
      // 两种商品都有，显示Tab
      pageData.showTabs = true;

      // 只在初始化时设置默认tab，其他时候保持当前tab
      if (type === "init" || !currentActiveTab) {
        pageData.activeTab = "direct";
      }

      // 根据当前activeTab设置对应的数据
      if (pageData.activeTab === "direct") {
        pageData.currentCartData = pageData.onlineOrderCartTab;
      } else {
        pageData.currentCartData = pageData.goodsLookingCartTab;
      }
    } else if (hasDirectPayment) {
      // 只有直接付款商品
      pageData.showTabs = false;
      pageData.activeTab = "direct";
      pageData.currentCartData = pageData.onlineOrderCartTab;
    } else if (hasQuotation) {
      // 只有询价商品
      pageData.showTabs = false;
      pageData.activeTab = "quotation";
      pageData.currentCartData = pageData.goodsLookingCartTab;
    }

    // 处理商品数据
    if (pageData.currentCartData?.goodsList) {
      processGoodsData(pageData.currentCartData.goodsList);
    }

    // 同步更新store的购物车数据
    authStore.getCartList(data);
  } else if (res?.result?.code === 403) {
    loginRegister?.openLogin();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

// 处理商品数据
function processGoodsData(goodsList: any[]) {
  goodsList.forEach((goods: any) => {
    goods.selected = goods.skuList.every((obj: any) => obj.selected);
    goods.skuTotalQuantity = 0;
    goods.skuSelectedQuantity = 0;

    goods.skuList.forEach((sku: any) => {
      if (!sku.minIncreaseQuantity) {
        sku.minIncreaseQuantity = goods.minIncreaseQuantity;
      }
      goods.skuTotalQuantity += sku.buyQty;
      if (sku.selected) {
        goods.skuSelected = true;
        goods.skuSelectedQuantity += sku.buyQty;
      }
    });
  });
}

// 使用接口返回的数据更新购物车状态
function updateCartDataFromResponse(data: any) {
  if (!data) return;

  // 设置Tab数据
  pageData.onlineOrderCartTab = data?.onlineOrderCartTab || null;
  pageData.goodsLookingCartTab = data?.goodsLookingCartTab || null;

  // 确定显示模式和默认Tab
  const hasDirectPayment = !!pageData.onlineOrderCartTab;
  const hasQuotation = !!pageData.goodsLookingCartTab;

  // 保存当前的activeTab状态
  const currentActiveTab = pageData.activeTab;

  if (hasDirectPayment && hasQuotation) {
    // 两种商品都有，显示Tab
    pageData.showTabs = true;

    // 根据当前activeTab设置对应的数据
    if (pageData.activeTab === "direct") {
      pageData.currentCartData = pageData.onlineOrderCartTab;
    } else {
      pageData.currentCartData = pageData.goodsLookingCartTab;
    }
  } else if (hasDirectPayment) {
    // 只有直接付款商品
    pageData.showTabs = false;
    pageData.activeTab = "direct";
    pageData.currentCartData = pageData.onlineOrderCartTab;
  } else if (hasQuotation) {
    // 只有询价商品
    pageData.showTabs = false;
    pageData.activeTab = "quotation";
    pageData.currentCartData = pageData.goodsLookingCartTab;
  }

  // 处理商品数据
  if (pageData.currentCartData?.goodsList) {
    processGoodsData(pageData.currentCartData.goodsList);
  }

  // 同步更新store的购物车数据
  authStore.getCartList(data);
}

// Tab切换
function switchTab(tab: "direct" | "quotation") {
  pageData.activeTab = tab;

  if (tab === "direct") {
    pageData.currentCartData = pageData.onlineOrderCartTab;
  } else {
    pageData.currentCartData = pageData.goodsLookingCartTab;
  }
}

// 全选 - 使用新的 updateSelectedAll 接口
async function onAllSelection(value: any, supportOnlineOrder?: boolean) {
  // 根据当前激活的tab确定supportOnlineOrder
  let isOnlineOrder = supportOnlineOrder;
  if (isOnlineOrder === undefined) {
    isOnlineOrder = pageData.activeTab === "direct";
  }

  const res: any = await useUpdateSelectedAll({
    supportOnlineOrder: isOnlineOrder,
    selected: value,
  });
  if (res?.result?.code === 200) {
    // 直接使用接口返回的购物车数据更新页面状态
    updateCartDataFromResponse(res?.data);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
// 商品选中
function onGoodsSelection(value: any, goods: any) {
  onUpdateCart({ selected: value, goodsId: goods.goodsId, padc: goods.padc });
}
// sku选中
function onSkuSelection(value: any, sku: any, goods: any) {
  onUpdateCart({ selected: value, skuId: sku.skuId, padc: sku.padc });
}
// sku数量修改
function onCartQtyUpdate(value: any, sku: any, goods: any) {
  onUpdateCart({
    quantity: value,
    selected: sku.selected,
    skuId: sku.skuId,
    padc: sku.padc,
  });
}
// 修改
async function onUpdateCart(params: any) {
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    // 直接使用接口返回的购物车数据更新页面状态
    updateCartDataFromResponse(res?.data);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
// 删除商品
async function onDeleteGoods(goods: any) {
  onRemoveCart({ goodsId: goods.goodsId, padc: goods.padc });
}
// 删除sku
async function onDeleteSku(sku: any, goods: any) {
  onRemoveCart({ skuId: sku.skuId, padc: sku.padc });
}
// 删除
async function onRemoveCart(params: any) {
  const res: any = await useRemoveCart(params);
  if (res?.result?.code === 200) {
    message.success(authStore.i18n("cm_find.deleteSuccessMessage"), {
      duration: 3000,
    });
    // 直接使用接口返回的购物车数据更新页面状态
    updateCartDataFromResponse(res?.data);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

async function onOpenSkuDialog(sku: any, goods: any) {
  const res: any = await useGoodsInfo({
    id: goods.goodsId,
    padc: sku.padc,
    deviceType: 1,
  });
  if (res?.result?.code === 200) {
    pageData.currentSku = sku;
    pageData.updatedSku = sku;
    pageData.currentGoods = res?.data;
    await onInitGoodsData();
    pageData.dialogVisible = true;
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

async function onInitGoodsData() {
  pageData.selectedSpec = {};
  pageData.currentSku.specItemList.forEach((spec: any) => {
    pageData.selectedSpec[spec.groupId] = spec.itemId;
  });
  // 只有一个规格时 需要在初始化的时候 去判断有没有库存以及上下架
  if (pageData.currentGoods?.specList.length == 1) {
    const onlyOneSpecItems = pageData.currentGoods?.specList[0].items;
    onlyOneSpecItems.forEach((t: any) => {
      t.disabled = true;
      const matchingSkus = pageData.currentGoods?.skuList.filter((sku: any) => {
        return sku.specItems.some(
          (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
        );
      });
      if (matchingSkus.length > 0) {
        matchingSkus.forEach((sku: any) => {
          let disabled = true;
          // 判断当前选中的sku是否是当前sku 如果是则还是可选的
          if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
            disabled = false;
          }
          if (sku.stockQty != 0 && sku.cartQty == 0) {
            disabled = false;
          }
          const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
          onlyOneSpecItems.forEach((item: any) => {
            if (item.itemId == matchingItemId) {
              item.disabled = disabled;
            }
          });
        });
      } else {
        onlyOneSpecItems.forEach((item: any) => {
          item.disabled = true;
        });
      }
    });
  }
  onFilterSku();
}

function onSpecUpdate(specId: string, itemId: string, item: any) {
  if (item.disabled) return;
  pageData.selectedSpec[specId] = itemId;
}

// 更新最后一组规格组的状态 最后一组规格会校验sku的库存以及下架状态 如果没有查到sku 默认下架
function updateSpecStatus() {
  // 将对象转成数组 方便处理
  let selectList = <any>[];
  for (const id in pageData.selectedSpec) {
    selectList.push(pageData.selectedSpec[id]);
  }
  // 这里需要将选中的最后一组的规格给移除掉 再去找匹配的sku
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  lastSpecList.items.forEach((item: any) => {
    if (selectList.includes(item.itemId)) {
      const existingIndex = selectList.indexOf(item.itemId);
      if (existingIndex !== -1) {
        selectList.splice(existingIndex, 1);
      }
    }
  });
  lastSpecList.items.forEach((t: any) => {
    t.disabled = true;
  });
  // 获取所有满足条件的SKU
  const matchingSkus = pageData.currentGoods.skuList.filter((sku: any) => {
    return selectList.every((specItemId: string) =>
      sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === specItemId
      )
    );
  });
  // 如果有满足条件的SKU，则更新规格的可选状态
  if (matchingSkus.length > 0) {
    matchingSkus.forEach((sku: any) => {
      let disabled = true;
      // 判断当前选中的sku是否是当前sku 如果是则还是可选的
      if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
        disabled = false;
      }
      // 如果库存为0 则不可选 如果cartQty选购的数量大于0 则是已加购的规格 也是不可选的
      if (sku.stockQty != 0 && sku.cartQty == 0) {
        disabled = false;
      }
      const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
      lastSpecList.items.forEach((item: any) => {
        if (item.itemId == matchingItemId) {
          item.disabled = disabled;
        }
      });
    });
  } else {
    lastSpecList.items.forEach((item: any) => {
      item.disabled = true;
    });
  }
  onFilterSku();
}
// 只有一个规格时 判断当前规格的sku有没有库存以及上下架
function updateOneSpecStatus() {
  const onlyOneSpecItems = pageData.currentGoods?.specList[0].items;
  onlyOneSpecItems.forEach((t: any) => {
    t.disabled = true;
    const matchingSkus = pageData.currentGoods?.skuList.filter((sku: any) => {
      return sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
      );
    });
    if (matchingSkus.length > 0) {
      matchingSkus.forEach((sku: any) => {
        let disabled = true;
        // 判断当前选中的sku是否是当前sku 如果是则还是可选的
        if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
          disabled = false;
        }
        if (sku.stockQty != 0 && sku.cartQty == 0) {
          disabled = false;
        }
        const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
        onlyOneSpecItems.forEach((item: any) => {
          if (item.itemId == matchingItemId) {
            item.disabled = disabled;
          }
        });
      });
    } else {
      onlyOneSpecItems.forEach((item: any) => {
        item.disabled = true;
      });
    }
  });
  onFilterSku();
}

// 筛选出sku
function onFilterSku() {
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  // 选中的sku是下架或者没有库存的情况下 将最后一组规格里选中规格的取消
  for (const id in pageData.selectedSpec) {
    lastSpecList.items.forEach((item: any) => {
      if (item.itemId == pageData.selectedSpec[id] && item.disabled) {
        delete pageData.selectedSpec[id];
      }
    });
  }
  const skus = pageData.currentGoods.skuList.filter((sku: any) => {
    return (
      Object.entries(pageData.selectedSpec).every(([specId, itemId]) => {
        const matchingSpec = sku.specItems.find(
          (spec: any) => spec.specId === specId
        );
        if (!matchingSpec || matchingSpec.itemId !== itemId) {
          return false;
        }
        return true;
      }) && Object.keys(pageData.selectedSpec).length === sku.specItems.length
    );
  });
  pageData.updatedSku = skus.length > 0 ? skus[0] : null;
}

async function onConfirm() {
  const params = {
    selected: true,
    updatedSkuId: pageData.updatedSku.id,
    skuId: pageData.currentSku.skuId,
    goodsId: pageData.currentGoods.id,
    padc: pageData.currentSku.padc,
  };
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    pageData.dialogVisible = false;
    // 直接使用接口返回的购物车数据更新页面状态
    updateCartDataFromResponse(res?.data);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

function onCancel() {
  pageData.dialogVisible = false;
}

function onGoHome() {
  window.location.href = "/";
}

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};

onMounted(() => {
  window.addEventListener("scroll", handleAnchorScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", handleAnchorScroll);
});

function handleAnchorScroll() {
  // 获取各个元素
  const footerElement = document.getElementById("page-footer");
  const formAffix = document.getElementById("submit-affix");

  if (footerElement && formAffix) {
    // 获取表单和页脚的高度
    const affixHeight = formAffix.offsetHeight;
    const footerHeight = footerElement.offsetHeight;
    // 判断文档高度是否小于(表单高度+页脚高度+80px误差)
    const criticalHeight = affixHeight + footerHeight + 80;
    // 文档高度足够，只需处理正常的固钉逻辑
    if (window.innerHeight >= criticalHeight) {
      formAffix.style.top = "20px";
    } else {
      const footerElement = document.getElementById("page-footer");
      const formAffix = document.getElementById("submit-affix");
      if (footerElement && formAffix) {
        const footerTop =
          footerElement.getBoundingClientRect().top + window.scrollY;
        const scrollTop =
          window.scrollY ||
          document.documentElement.scrollTop ||
          document.body.scrollTop ||
          0;
        const windowHeight =
          window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight;

        // 计算距离底部的距离
        const distanceFromFooter = footerTop - (scrollTop + windowHeight);

        // 判断是否滚动到page-footer元素的位置
        if (distanceFromFooter <= 0) {
          // 计算right-affix的top值
          formAffix.style.top = distanceFromFooter + "px";
        } else {
          // 如果没有滚动到footer的位置，保持form-affix的初始top值
          formAffix.style.top = 20 + "px";
        }
      }
    }
  }
}

function onGoLoginRegister() {
  loginRegister?.openLogin(); //未登录 去登录
}
</script>
<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 90vh;
}

:deep(.divider .n-divider) {
  margin: 0px;
}
:deep(.n-divider__title) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}
.divider.n-divider:not(.n-divider--vertical) {
  margin: 0px;
}
:deep(.divider .n-divider__line.n-divider__line--left) {
  display: none;
}
.spec-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 2.5rem;
  margin: 0.4rem 1rem 0.4rem 0;
  padding: 0 1.1rem;
  min-width: 40px;
  cursor: pointer;
  color: #797979;
  border-radius: 5px;
  border: 1px solid #d7d7d7;
}
.disabled-btn {
  background: #d7d7d7;
  cursor: not-allowed;
  border: none;
}
.current-btn {
  color: #e50113;
  cursor: pointer;
  border: none;
  background: rgba(229, 1, 19, 0.18);
  .btn-tick {
    opacity: 1;
  }
}
.btn-tick {
  position: absolute;
  right: -10px;
  bottom: 0px;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.btn-tick::before {
  content: ""; /* 添加伪类元素的内容 */
  position: absolute; /* 将伪类元素相对于父元素定位 */
  top: 5px;
  right: 2px;
  bottom: 1px;
  left: 1px;
  background-color: #e50113; /* 设置背景色 */
  z-index: -1; /* 将伪类元素放在父元素的下方 */
  border-radius: 8px 0 5px 0;
}

.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}

:deep(.country-delivery) {
  font-size: 16px;
  margin-right: 10px;
}
:deep(.country-code) {
  font-size: 16px;
}
:deep(.popover-country) {
  width: 100%;
  padding: 0;
  justify-content: space-between;
  .country-code {
    line-height: 16px !important;
  }
}
</style>
