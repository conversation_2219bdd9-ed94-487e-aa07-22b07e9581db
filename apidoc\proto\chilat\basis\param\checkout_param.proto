syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/param/sales_order_param.proto";
import "common.proto";
import "common/business.proto";



//解析购买的SKU文本的接口参数
message ParseBuySkuTextParam {
    string skuText = 20; //以文本方式批量添加的SKU信息（多个SKU，总是以换行符“\n”分隔）
    string headLine = 30; //头部行（即字段名行，总是用制表符“\t”分隔）
}

//预览购买的SKU列表的接口参数
message PreviewBuySkuListParam {
    repeated CheckoutSkuParam skuList = 10; //SKU列表
}

//购买的SKU信息
message CheckoutSkuParam {
    string skuId = 10; // sku id
    int32 goodsQty = 20; //下单数量
    double buyPrice = 30; //结算单价（TODO：业务员或客服改价，须检查权限）
    string padc = 88; //促销活动动态代码
    //    string spm = 100; //SPM跟踪码
}

//计算预估运费的参数
message MidEstimateFreightParam {
    int32 siteId = 10; // 当前站点ID（即页面选中的配送国家ID；必填）
    string routeId = 20; //当前页面选中的线路（不传，则取默认值）
    bool ignoreRouteIdError = 30; //忽略线路ID错误（从购物车中取到的线路，作为默认传参时，将此参数置为true）
    map<string, int32> skuQuantityMap = 40; //sku数量Map（KEY: skuId, VALUE: quantity）
}

//快速下单参数
message MidFastCreateOrderParam {
    string userId = 10; //用户ID（必填，mall端取登录用户ID）
    int32 siteId = 20; //站点ID（必填，当前购物车页面中的国家ID）
    OrderAddressParam orderAddress = 30; //配送信息（必填）
    repeated MidCheckoutSkuParam skuList = 100; //结算商品列表（必填）

    //  string countryId = 30; //支付国家ID（订单归属国家；若不传值，默认取收货地址中的国家）
    //  repeated SalesOrderRouteFeeParam routeFeeList = 40; //线路总运费（仅当 isRouteFeeInBox=false 才可能需要传值）
    //  repeated string goodsLookingNoList = 50; //关联询盘单号
    //  string buyerRemark = 60; //客户备注（最大1000字）
    //  string sellerRemark = 70; //卖家备注（最大1000字）
    //  common.QuotationMode priceModel = 80; //报价模式
    //  common.PayMode paymentModel = 90; //支付模式:0.一次性支付 100.首次支付只支付国内费用，二次支付待仓库收货之后再支付
    //  OrderAddressParam orderAddress = 100; //配送信息
    //  repeated SalesOrderBoxParam salesOrderBoxList= 110; //装箱信息
    //  repeated SalesOrderSundryFeeParam salesOrderFeeList = 120; //费用信息
    //  repeated SalesOrderLineParam soLineList = 130; //订单行信息
    //  double commission = 140; //佣金
    //  double goodsDiscountAmount = 150; //商品优惠金额（默认0，大于0表示享受了商品优惠）
    //  bool isRouteFeeInBox = 160; //装箱信息中，是否包含线路运费（必填）
    //  string salesManUserId = 170; //业务员Id

}

//结算商品信息
message MidCheckoutSkuParam {
    string skuId = 10; //SKU ID（必填）
    int32 quantity = 20; //购买数量（必填）
    string routeId = 30; //线路ID（必填，暂不支持多线路同时下单）
    string spm = 40; //SPM跟踪码
    string padc = 88; //促销活动动态代码
}

