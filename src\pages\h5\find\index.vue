<template>
  <div class="mobile-container">
    <!-- Tab切换区域 -->
    <div
      v-if="pageData.showTabs"
      class="bg-[#F2F2F2] mx-[0.16rem] mt-[0.16rem] rounded-[0.4rem] p-[0.08rem] h-[58px] mb-[8px]"
    >
      <div class="flex">
        <div
          v-if="pageData.onlineOrderCartTab"
          @click="switchTab('direct')"
          :class="[
            'flex-1 flex items-center justify-center py-[0.26rem] px-[0.28rem] rounded-[0.32rem]  transition-all duration-300 gap-[6px] relative',
            pageData.activeTab === 'direct' ? 'bg-[#FFF]' : 'bg-transparent',
          ]"
        >
          <img
            src="@/assets/icons/find/payment.svg"
            alt="payment"
            class="w-[0.48rem] h-[0.48rem]"
          />
          <span class="text-[16px] leading-[16px]">
            {{ authStore.i18n("cm_find.directPaymentGoods") }}
          </span>
          <div
            class="w-[18px] h-[18px] rounded-full bg-[#e50113] text-[0.24rem] leading-[0.24rem] absolute top-[-9px] right-[0px] flex items-center justify-center text-[#fff]"
          >
            {{ pageData.onlineOrderCartTab.stat?.skuCount || 0 }}
          </div>
        </div>

        <div
          v-if="pageData.goodsLookingCartTab"
          @click="switchTab('quotation')"
          :class="[
            'flex-1 flex items-center justify-center py-[0.26rem] px-[0.28rem] rounded-[0.32rem]  transition-all duration-300 gap-[6px] relative',
            pageData.activeTab === 'quotation' ? 'bg-[#FFF]' : 'bg-transparent',
          ]"
        >
          <img
            src="@/assets/icons/find/quotation.svg"
            alt="quotation"
            class="w-[0.48rem] h-[0.48rem]"
          />
          <span class="text-[16px] leading-[16px]">
            {{ authStore.i18n("cm_find.inquiryGoods") }}
          </span>
          <span
            class="w-[18px] h-[18px] rounded-full bg-[#e50113] text-[0.24rem] leading-[0.24rem] absolute top-[-9px] right-[0px] flex items-center justify-center text-[#fff]"
          >
            {{ pageData.goodsLookingCartTab.stat?.skuCount || 0 }}
          </span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-if="!pageData.loading" class="content-section">
      <!-- Tab内容 -->
      <div v-if="pageData.showTabs">
        <!-- 直接付款商品Tab -->
        <DirectPaymentTab
          v-if="pageData.activeTab === 'direct' && pageData.onlineOrderCartTab"
          :cartData="pageData.onlineOrderCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          :showTabs="pageData.showTabs"
        />

        <!-- 询价商品Tab -->
        <QuotationRequiredTab
          v-if="
            pageData.activeTab === 'quotation' && pageData.goodsLookingCartTab
          "
          :cartData="pageData.goodsLookingCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          @onInquiry="onGoFindSubmit"
          :showTabs="pageData.showTabs"
        />
      </div>

      <!-- 无Tab时的内容 -->
      <div v-else>
        <!-- 直接付款商品 -->
        <DirectPaymentTab
          v-if="pageData.onlineOrderCartTab"
          :cartData="pageData.onlineOrderCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          :showTabs="pageData.showTabs"
        />

        <!-- 询价商品 -->
        <QuotationRequiredTab
          v-if="pageData.goodsLookingCartTab"
          :cartData="pageData.goodsLookingCartTab"
          @onAllSelection="onAllSelection"
          @onGoodsSelection="onGoodsSelection"
          @onSkuSelection="onSkuSelection"
          @onCartQtyUpdate="onCartQtyUpdate"
          @onDeleteGoods="onDeleteGoods"
          @onDeleteSku="onDeleteSku"
          @onOpenSkuDialog="onOpenSkuDialog"
          @onInquiry="onGoFindSubmit"
          :showTabs="pageData.showTabs"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="!getCurrentCartData()?.goodsList?.length" class="empty-state">
        <n-empty
          :description="authStore.i18n('cm_find.noData')"
          class="mt-[1.92rem] text-[0.28rem]"
        >
          <template #extra>
            <n-button
              size="small"
              color="#E50113"
              text-color="#fff"
              @click="onGoHome"
              class="text-[0.28rem] h-[0.56rem] px-[0.2rem]"
            >
              {{ authStore.i18n("cm_find.goHome") }}
            </n-button>
          </template>
        </n-empty>
      </div>
    </div>

    <div v-show="pageData.loading" class="loading-overlay">
      <n-spin stroke="#e50113" :show="pageData.loading"> </n-spin>
    </div>

    <mobile-tab-bar :naiveBar="3" />

    <n-drawer
      v-model:show="pageData.dialogVisible"
      resizable
      default-width="100%"
      default-height="90%"
      placement="bottom"
      :on-after-leave="onCancel"
    >
      <n-drawer-content>
        <template #header></template>
        <!-- 商品规格 -->
        <div>
          <div
            v-for="spec in pageData.currentGoods.specList"
            :key="spec.id"
            class="ml-[0.16rem] text-[0.28rem]"
          >
            <div class="mb-[0.08rem] font-medium">{{ spec.name }}:</div>
            <div class="flex flex-wrap mb-[0.16rem]">
              <div
                v-for="item in spec.items"
                :key="item.itemId"
                @click="onSpecUpdate(spec.id, item.itemId, item)"
                class="spec-btn min-w-[0.8rem] max-w-4/5 relative"
                :class="{
                  'current-btn': pageData.selectedSpec[spec?.id] == item.itemId,
                  'disabled-btn': item.disabled,
                }"
              >
                <n-image
                  lazy
                  preview-disabled
                  object-fit="fill"
                  class="w-[0.72rem] h-[0.72rem] shrink-0 mr-[0.24rem]"
                  :src="item.imageUrl"
                  v-if="item.imageUrl"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
                <span class="py-[0.16rem]">{{ item.itemName }}</span>
                <icon-card
                  size="20"
                  name="typcn:tick"
                  color="#fff"
                  class="btn-tick mr-[0.16rem]"
                ></icon-card>
              </div>
            </div>
          </div>
        </div>
        <template #footer
          ><div class="flex w-full">
            <n-button
              @click="onConfirm"
              color="#E50113"
              class="mr-[0.32rem] flex-1 text-[0.28rem] h-[0.68rem]"
              >{{ authStore.i18n("cm_find.confirm") }}</n-button
            >
            <n-button
              @click="onCancel"
              class="flex-1 text-[0.28rem] h-[0.68rem]"
              >{{ authStore.i18n("cm_find.cancel") }}</n-button
            >
          </div></template
        >
      </n-drawer-content>
    </n-drawer>
  </div>
</template>
<script setup lang="ts">
const message = useMessage();
import { useAuthStore } from "@/stores/authStore";

import DirectPaymentTab from "./components/DirectPaymentTab.vue";
import QuotationRequiredTab from "./components/QuotationRequiredTab.vue";

const authStore = useAuthStore();
const pageData = reactive<any>({
  // Tab相关
  activeTab: "direct", // 'direct' | 'quotation'
  showTabs: false,

  // 购物车数据
  onlineOrderCartTab: null, // 直接付款商品Tab数据
  goodsLookingCartTab: null, // 询价商品Tab数据
  currentCartData: null, // 当前显示的购物车数据

  // 弹窗状态
  dialogVisible: false,
  errorDialogVisible: false,
  submitDialogVisible: false,
  showTipDrawer: false,
  errorMessage: "",

  // 商品规格选择
  currentSku: <any>{},
  updatedSku: <any>{},
  currentGoods: <any>{},
  selectedSpec: <any>{},

  // 加载状态
  loading: false,
  submitLoading: false,
});

watch(
  () => pageData.selectedSpec,
  (newVal: any) => {
    // 多组规格时 选中倒数第二组的时候 需要检验sku的库存以及上下架状态
    if (
      newVal &&
      pageData.currentGoods?.specList?.length > 1 &&
      Object.values(newVal).length > pageData.currentGoods?.specList.length - 2
    ) {
      const targetId =
        pageData.currentGoods?.specList[
          pageData.currentGoods?.specList?.length - 2
        ].id;
      for (const id in pageData.selectedSpec) {
        if (targetId == id) {
          updateSpecStatus();
          break;
        }
      }
    } else {
      //只有一组时 直接校验sku的库存以及上下架状态
      updateOneSpecStatus();
    }
  },
  { deep: true }
);

// 初始化页面数据
onGetCartByTab("init");

// 获取购物车列表（按Tab分割）
async function onGetCartByTab(type?: string) {
  if (type === "init") {
    pageData.loading = true;
  }
  const mockData = {
    result: { code: 200, message: "success" },
    data: {
      onlineOrderCartTab: {
        stat: {
          goodsCount: 3,
          onlineSkuCount: 6,
          selectSkuCount: 6,
          selectSkuTotalQuantity: 598,
          selectTotalSalePrice: 557.12,
          skuCount: 6,
          totalSalePrice: 557.12,
          selectTotalCommission: 55.71,
        },
        goodsList: [
          {
            goodsId: "1935669152516079618",
            goodsName:
              "Helado de burbujas en forma de 4 227 molde de silicona pastel helado pudín molde de postre jabón para hornear utensilios",
            goodsNo: "WB-250619-4804",
            goodsPriceUnitName: "pcs",
            mainImageUrl:
              "https://cbu01.alicdn.com/img/ibank/O1CN01yYp7rO26ClHcvi2eu_!!*************-0-cib.jpg?x-oss-process=image/resize,m_fill,h_300,w_300,limit_0",
            minBuyQuantity: 2,
            minIncreaseQuantity: 1,
            selectedGoodsQty: 420,
            supportOnlineOrder: true,
            selected: true,
            skuSelected: true,
            skuTotalQuantity: 420,
            skuSelectedQuantity: 420,
            specItemList: [
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01aJusso26ClHcvi2eD_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Fideos de arroz",
                itemNameCn: "米粉",
              },
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01VvmIRM26ClHcvi2gM_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Caballo verde",
                itemNameCn: "马绿",
              },
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01avJX0H26ClHfmQfDA_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Azul nórdico",
                itemNameCn: "北欧蓝",
              },
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01nhMyiI26ClHZDUad9_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Blanco",
                itemNameCn: "白色",
              },
            ],
            skuList: [
              {
                buyQty: 210,
                goodsId: "1935669152516079618",
                minIncreaseQuantity: 210,
                salePrice: 0.59,
                selected: true,
                skuId: "1935669152591577089",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01aJusso26ClHcvi2eD_!!*************-0-cib.jpg",
                skuNo: "WB-250619-4804-1",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Colores",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01aJusso26ClHcvi2eD_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Fideos de arroz",
                    itemNameCn: "米粉",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmWk72Hcba9887Zx4BFrZ.8721.homepage-tag-goods..1933077091745067010",
                stepPrices: [
                  {
                    end: -1,
                    insideOnePrice: 0.59,
                    price: 0.59,
                    start: 2,
                  },
                ],
                stockQty: 2342342,
                subtotalSalePrice: 123.9,
                estimateFreight: 0,
              },
              {
                buyQty: 210,
                goodsId: "1935669152516079618",
                minIncreaseQuantity: 210,
                salePrice: 0.59,
                selected: true,
                skuId: "1935669152721600514",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01nhMyiI26ClHZDUad9_!!*************-0-cib.jpg",
                skuNo: "WB-250619-4804-4",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Colores",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01nhMyiI26ClHZDUad9_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Blanco",
                    itemNameCn: "白色",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmWk72Hcba9887Zx4BFrZ.8721.homepage-tag-goods..1933077091745067010",
                stepPrices: [
                  {
                    end: -1,
                    insideOnePrice: 0.59,
                    price: 0.59,
                    start: 2,
                  },
                ],
                stockQty: 2342343,
                subtotalSalePrice: 123.9,
                estimateFreight: 0,
              },
            ],
          },
          {
            goodsId: "1794910253614735362",
            goodsName:
              "Disney/Disney Genuine DN03 semi-en la oreja Mini auricular Bluetooth inalámbrico pequeño y verdadero con cancelación de ruido Bluetooth",
            goodsNo: "WB-240527-30",
            goodsPriceUnitName: "pcs",
            mainImageUrl:
              "https://nhci-aigc.oss-cn-zhangjiakou.aliyuncs.com/ppc-records%2Fimage-translation%2F04b52a10-ca4c-11ee-9702-00163e0dc1b4.png?OSSAccessKeyId=LTAI5tCv9DpB7gYic1oGsAyv&Expires=4924485834&Signature=6T7j6VvVvisVkRttCxMH9E7Hv1Q%3D",
            minBuyQuantity: 1,
            minIncreaseQuantity: 11,
            routeId: "1844943559392428034",
            selectedGoodsQty: 33,
            supportOnlineOrder: true,
            selected: true,
            skuSelected: true,
            skuTotalQuantity: 33,
            skuSelectedQuantity: 33,
            estimateFreight: 37897.9,
            specItemList: [
              {
                groupId: "****************",
                groupName: "Color",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01MbIupb1cCMOk5AX1c_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Oso de fresa",
                itemNameCn: "草莓熊",
              },
              {
                groupId: "****************",
                groupName: "Color",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01sECnQv1cCMOrOhnbv_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Mickey",
                itemNameCn: "米奇",
              },
              {
                groupId: "****************",
                groupName: "Color",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01ASJFOm1cCMOmsfPZh_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Información sobre Minnie",
                itemNameCn: "米妮",
              },
              {
                groupId: "****************",
                groupName: "Color",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01bFLC0X1cCMcbrXqej_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Blanco",
                itemNameCn: "白色",
              },
              {
                groupId: "****************",
                groupName: "Color",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01PnxCbW1cCMcZjF03k_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Negro",
                itemNameCn: "黑色",
              },
            ],
            skuList: [
              {
                buyQty: 11,
                estimateFreight: 12632.63,
                goodsId: "1794910253614735362",
                salePrice: 5.99,
                selected: true,
                skuId: "1794910253627318274",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01MbIupb1cCMOk5AX1c_!!*************-0-cib.jpg",
                skuNo: "WB-240527-30-1",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Color",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01MbIupb1cCMOk5AX1c_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Oso de fresa",
                    itemNameCn: "草莓熊",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmWk72Hcba9887Zx4BFrZ.76006.homepage-tag-goods..********",
                stepPrices: [
                  {
                    end: 199,
                    insideOnePrice: 5.99,
                    price: 5.99,
                    start: 1,
                  },
                  {
                    end: 999,
                    insideOnePrice: 5.66,
                    price: 5.66,
                    start: 200,
                  },
                  {
                    end: -1,
                    insideOnePrice: 5.34,
                    price: 5.34,
                    start: 1000,
                  },
                ],
                stockQty: 497,
                subtotalSalePrice: 65.89,
              },
              {
                buyQty: 11,
                estimateFreight: 12632.63,
                goodsId: "1794910253614735362",
                salePrice: 5.99,
                selected: true,
                skuId: "1794910253639901187",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01sECnQv1cCMOrOhnbv_!!*************-0-cib.jpg",
                skuNo: "WB-240527-30-2",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Color",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01sECnQv1cCMOrOhnbv_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Mickey",
                    itemNameCn: "米奇",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmWk72Hcba9887Zx4BFrZ.76006.homepage-tag-goods..********",
                stepPrices: [
                  {
                    end: 199,
                    insideOnePrice: 5.99,
                    price: 5.99,
                    start: 1,
                  },
                  {
                    end: 999,
                    insideOnePrice: 5.66,
                    price: 5.66,
                    start: 200,
                  },
                  {
                    end: -1,
                    insideOnePrice: 5.34,
                    price: 5.34,
                    start: 1000,
                  },
                ],
                stockQty: 913,
                subtotalSalePrice: 65.89,
              },
              {
                buyQty: 11,
                estimateFreight: 12632.63,
                goodsId: "1794910253614735362",
                salePrice: 5.99,
                selected: true,
                skuId: "1794910253648289794",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01ASJFOm1cCMOmsfPZh_!!*************-0-cib.jpg",
                skuNo: "WB-240527-30-3",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Color",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01ASJFOm1cCMOmsfPZh_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Información sobre Minnie",
                    itemNameCn: "米妮",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmWk72Hcba9887Zx4BFrZ.76006.homepage-tag-goods..********",
                stepPrices: [
                  {
                    end: 199,
                    insideOnePrice: 5.99,
                    price: 5.99,
                    start: 1,
                  },
                  {
                    end: 999,
                    insideOnePrice: 5.66,
                    price: 5.66,
                    start: 200,
                  },
                  {
                    end: -1,
                    insideOnePrice: 5.34,
                    price: 5.34,
                    start: 1000,
                  },
                ],
                stockQty: 432,
                subtotalSalePrice: 65.89,
              },
            ],
          },
          {
            goodsId: "1783764008141602818",
            goodsName:
              "Moda simple europea y americana encantadora ZIRCON titanio acero collar nicho ins estilo elegante cadena de clavícula Internet celebridad luz colgante de lujo",
            goodsNo: "WB-240426-1670",
            goodsPriceUnitName: "pcs",
            mainImageUrl:
              "https://global-img-cdn.1688.com/img/ibank/O1CN01UDMwtA2N0FjRO4AbL_!!**********-0-cib.jpg?x-oss-process=image/resize,m_fill,h_300,w_300,limit_0",
            minBuyQuantity: 1,
            minIncreaseQuantity: 1,
            selectedGoodsQty: 145,
            supportOnlineOrder: true,
            selected: true,
            skuSelected: true,
            skuTotalQuantity: 145,
            skuSelectedQuantity: 145,
            specItemList: [
              {
                groupId: "****************",
                groupName: "Color",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01sANqqx2N0FjVTeBTT_!!**********-0-cib.jpg",
                itemId: "****************",
                itemName: "Corazón titanio acero necklace-X506 oro",
              },
            ],
            skuList: [
              {
                buyQty: 145,
                goodsId: "1783764008141602818",
                minIncreaseQuantity: 145,
                salePrice: 0.77,
                selected: true,
                skuId: "1783764008154185729",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01sANqqx2N0FjVTeBTT_!!**********-0-cib.jpg",
                skuNo: "WB-240426-1670-1",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Color",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01sANqqx2N0FjVTeBTT_!!**********-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Corazón titanio acero necklace-X506 oro",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmWk72Hcba9887Zx4BFrZ.97909.tag-goods-packing..********",
                stepPrices: [
                  {
                    end: 11,
                    insideOnePrice: 0.84,
                    price: 0.84,
                    start: 1,
                  },
                  {
                    end: 35,
                    insideOnePrice: 0.79,
                    price: 0.79,
                    start: 12,
                  },
                  {
                    end: -1,
                    insideOnePrice: 0.77,
                    price: 0.77,
                    start: 36,
                  },
                ],
                stockQty: 10000,
                subtotalSalePrice: 111.65,
                estimateFreight: 0,
              },
            ],
          },
        ],
        totalEstimateFreight: 37897.9,
        partEstimateFreight: 37897.9,
        freightGoodsQty: 33,
      },
      goodsLookingCartTab: {
        stat: {
          goodsCount: 3,
          onlineSkuCount: 5,
          selectSkuCount: 5,
          selectSkuTotalQuantity: 285,
          selectTotalSalePrice: 246.45,
          skuCount: 5,
          totalSalePrice: 246.45,
          selectTotalCommission: 0,
        },
        goodsList: [
          {
            goodsId: "1783764008141602818",
            goodsName:
              "Moda simple europea y americana encantadora ZIRCON titanio acero collar nicho ins estilo elegante cadena de clavícula Internet celebridad luz colgante de lujo",
            goodsNo: "WB-240426-1670",
            goodsPriceUnitName: "pcs",
            mainImageUrl:
              "https://global-img-cdn.1688.com/img/ibank/O1CN01UDMwtA2N0FjRO4AbL_!!**********-0-cib.jpg?x-oss-process=image/resize,m_fill,h_300,w_300,limit_0",
            minBuyQuantity: 1,
            minIncreaseQuantity: 1,
            selectedGoodsQty: 145,
            supportOnlineOrder: false,
            selected: true,
            skuSelected: true,
            skuTotalQuantity: 145,
            skuSelectedQuantity: 145,
            specItemList: [
              {
                groupId: "****************",
                groupName: "Color",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01sANqqx2N0FjVTeBTT_!!**********-0-cib.jpg",
                itemId: "****************",
                itemName: "Corazón titanio acero necklace-X506 oro",
              },
            ],
            skuList: [
              {
                buyQty: 145,
                goodsId: "1783764008141602818",
                minIncreaseQuantity: 145,
                salePrice: 0.77,
                selected: true,
                skuId: "1783764008154185729",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01sANqqx2N0FjVTeBTT_!!**********-0-cib.jpg",
                skuNo: "WB-240426-1670-1",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Color",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01sANqqx2N0FjVTeBTT_!!**********-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Corazón titanio acero necklace-X506 oro",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmcs5IyLrnMBPZGizuBSm.9898.tag-goods-packing..********",
                stepPrices: [
                  {
                    end: 11,
                    insideOnePrice: 0.84,
                    price: 0.84,
                    start: 1,
                  },
                  {
                    end: 35,
                    insideOnePrice: 0.79,
                    price: 0.79,
                    start: 12,
                  },
                  {
                    end: -1,
                    insideOnePrice: 0.77,
                    price: 0.77,
                    start: 36,
                  },
                ],
                stockQty: 10000,
                subtotalSalePrice: 111.65,
                estimateFreight: 0,
              },
            ],
          },
          {
            goodsId: "1783763343969370113",
            goodsName:
              "Nueva moda de dos colores Luna estrella conejo Zodiaco lindo animal dulce delicado collar para las mujeres",
            goodsNo: "WB-240426-1600",
            goodsPriceUnitName: "pcs",
            mainImageUrl:
              "https://global-img-cdn.1688.com/img/ibank/O1CN015uxH0D1lgR3DjAHur_!!*************-0-cib.jpg?x-oss-process=image/resize,m_fill,h_300,w_300,limit_0",
            minBuyQuantity: 1,
            minIncreaseQuantity: 1,
            selectedGoodsQty: 80,
            supportOnlineOrder: false,
            selected: true,
            skuSelected: true,
            skuTotalQuantity: 80,
            skuSelectedQuantity: 80,
            specItemList: [
              {
                groupId: "****************",
                groupName: "Color",
                itemId: "****************",
                itemName: "Dos tonos",
              },
            ],
            skuList: [
              {
                buyQty: 80,
                goodsId: "1783763343969370113",
                minIncreaseQuantity: 80,
                salePrice: 1.58,
                selected: true,
                skuId: "1783763343986147330",
                skuImage:
                  "https://global-img-cdn.1688.com/img/ibank/O1CN015uxH0D1lgR3DjAHur_!!*************-0-cib.jpg?x-oss-process=image/resize,m_fill,h_300,w_300,limit_0",
                skuNo: "WB-240426-1600-1",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Color",
                    itemId: "****************",
                    itemName: "Dos tonos",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmcs5IyLrnMBPZGizuBSm.17354.tag-goods-packing..********",
                stepPrices: [
                  {
                    end: -1,
                    insideOnePrice: 1.58,
                    price: 1.58,
                    start: 1,
                  },
                ],
                stockQty: 10000,
                subtotalSalePrice: 126.4,
                estimateFreight: 0,
              },
            ],
          },
          {
            goodsId: "1869627856849063938",
            goodsName:
              "Venta directa de fábrica caja con ruedas retráctiles ultrapequeñas, mini caja de almacenamiento de joyería transparente móvil, caja linda al por mayor",
            goodsNo: "WB-241219-160",
            goodsPriceUnitName: "PCS",
            mainImageUrl:
              "https://cbu01.alicdn.com/img/ibank/O1CN01ny683d1hn4N9hYnJL_!!*************-0-cib.jpg?x-oss-process=image/resize,m_fill,h_300,w_300,limit_0",
            minBuyQuantity: 1,
            minIncreaseQuantity: 20,
            routeId: "1844943559392428034",
            selectedGoodsQty: 60,
            supportOnlineOrder: false,
            selected: true,
            skuSelected: true,
            skuTotalQuantity: 60,
            skuSelectedQuantity: 60,
            estimateFreight: 90,
            specItemList: [
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01HphGHF1hn4M8Fq8Xf_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Maleta trolley pequeña negra",
                itemNameCn: "小拉杆箱  黑色",
              },
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01x1JIcb1hn4MywjRaK_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Pequeña caja trolley rosa",
                itemNameCn: "小拉杆箱     粉色",
              },
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01lj9Cgr1hn4PFGmk47_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Pequeña caja con ruedas azul",
                itemNameCn: "小拉杆箱     蓝色",
              },
              {
                groupId: "****************",
                groupName: "Colores",
                imageUrl:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01XzDN7v1hn4PGJ4Vv0_!!*************-0-cib.jpg",
                itemId: "****************",
                itemName: "Pequeño carro blanco",
                itemNameCn: "小拉杆箱     白色",
              },
            ],
            skuList: [
              {
                buyQty: 20,
                estimateFreight: 30,
                goodsId: "1869627856849063938",
                salePrice: 0.14,
                selected: true,
                skuId: "1869627856870035458",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01x1JIcb1hn4MywjRaK_!!*************-0-cib.jpg",
                skuNo: "WB-241219-160-2",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Colores",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01x1JIcb1hn4MywjRaK_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Pequeña caja trolley rosa",
                    itemNameCn: "小拉杆箱     粉色",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmcN4ptLWha7J5pwiz80G.25688.homepage-tag-goods..********",
                stepPrices: [
                  {
                    end: 499,
                    insideOnePrice: 0.14,
                    price: 0.14,
                    start: 1,
                  },
                  {
                    end: -1,
                    insideOnePrice: 0.14,
                    price: 0.14,
                    start: 500,
                  },
                ],
                stockQty: 562284,
                subtotalSalePrice: 2.8,
              },
              {
                buyQty: 20,
                estimateFreight: 30,
                goodsId: "1869627856849063938",
                salePrice: 0.14,
                selected: true,
                skuId: "1869627856878424067",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01lj9Cgr1hn4PFGmk47_!!*************-0-cib.jpg",
                skuNo: "WB-241219-160-3",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Colores",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01lj9Cgr1hn4PFGmk47_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Pequeña caja con ruedas azul",
                    itemNameCn: "小拉杆箱     蓝色",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmcN4ptLWha7J5pwiz80G.25688.homepage-tag-goods..********",
                stepPrices: [
                  {
                    end: 499,
                    insideOnePrice: 0.14,
                    price: 0.14,
                    start: 1,
                  },
                  {
                    end: -1,
                    insideOnePrice: 0.14,
                    price: 0.14,
                    start: 500,
                  },
                ],
                stockQty: 87390,
                subtotalSalePrice: 2.8,
              },
              {
                buyQty: 20,
                estimateFreight: 30,
                goodsId: "1869627856849063938",
                salePrice: 0.14,
                selected: true,
                skuId: "1869627856886812675",
                skuImage:
                  "https://cbu01.alicdn.com/img/ibank/O1CN01XzDN7v1hn4PGJ4Vv0_!!*************-0-cib.jpg",
                skuNo: "WB-241219-160-4",
                specItemList: [
                  {
                    groupId: "****************",
                    groupName: "Colores",
                    imageUrl:
                      "https://cbu01.alicdn.com/img/ibank/O1CN01XzDN7v1hn4PGJ4Vv0_!!*************-0-cib.jpg",
                    itemId: "****************",
                    itemName: "Pequeño carro blanco",
                    itemNameCn: "小拉杆箱     白色",
                  },
                ],
                spm: "04glyiK6kfC9PzHW45iP6FxYzmcN4ptLWha7J5pwiz80G.25688.homepage-tag-goods..********",
                stepPrices: [
                  {
                    end: 499,
                    insideOnePrice: 0.14,
                    price: 0.14,
                    start: 1,
                  },
                  {
                    end: -1,
                    insideOnePrice: 0.14,
                    price: 0.14,
                    start: 500,
                  },
                ],
                stockQty: 883516,
                subtotalSalePrice: 2.8,
              },
            ],
          },
        ],
        totalEstimateFreight: 90,
        partEstimateFreight: 90,
        freightGoodsQty: 60,
      },
      lastInquiry: {
        goodsLookingNo: "INQ202412140001",
        totalGoodsCount: 3,
        subTotal: 138.4,
        skus: [],
        remark: "Necesito cotización urgente",
        id: "inquiry_001",
        submitTime: Date.now() - 86400000, // 1天前
        countryName: "México",
        whatsapp: "+52 1234567890",
        submitName: "Juan Pérez",
        email: "<EMAIL>",
        postcode: "01000",
        address: "Av. Reforma 123, Ciudad de México",
        countryId: "MX",
        areaCode: "+52",
      },
    },
  };
  // 使用真实接口调用
  // const res: any = await useGetCartByTab({});

  // 使用 mock 数据进行测试
  const res: any = mockData;

  if (type === "init") {
    pageData.loading = false;
  }

  if (res?.result?.code === 200) {
    const data = res?.data;

    // 设置Tab数据
    pageData.onlineOrderCartTab = data?.onlineOrderCartTab || null;
    pageData.goodsLookingCartTab = data?.goodsLookingCartTab || null;

    // 确定显示模式和默认Tab
    const hasDirectPayment = !!pageData.onlineOrderCartTab;
    const hasQuotation = !!pageData.goodsLookingCartTab;

    // 保存当前的activeTab状态
    const currentActiveTab = pageData.activeTab;

    if (hasDirectPayment && hasQuotation) {
      // 两种商品都有，显示Tab
      pageData.showTabs = true;

      // 只在初始化时设置默认tab，其他时候保持当前tab
      if (type === "init" || !currentActiveTab) {
        pageData.activeTab = "direct";
      }

      // 根据当前activeTab设置对应的数据
      if (pageData.activeTab === "direct") {
        pageData.currentCartData = pageData.onlineOrderCartTab;
      } else {
        pageData.currentCartData = pageData.goodsLookingCartTab;
      }
    } else if (hasDirectPayment) {
      // 只有直接付款商品
      pageData.showTabs = false;
      pageData.activeTab = "direct";
      pageData.currentCartData = pageData.onlineOrderCartTab;
    } else if (hasQuotation) {
      // 只有询价商品
      pageData.showTabs = false;
      pageData.activeTab = "quotation";
      pageData.currentCartData = pageData.goodsLookingCartTab;
    }

    // 处理商品数据
    if (pageData.currentCartData?.goodsList) {
      processGoodsData(pageData.currentCartData.goodsList);
    }

    // 兼容旧版本字段
    pageData.goodsList = pageData.currentCartData?.goodsList || [];
    pageData.stat = pageData.currentCartData?.stat || {};
    pageData.totalEstimateFreight =
      pageData.currentCartData?.totalEstimateFreight || null;

    // 同步更新store的购物车数据
    authStore.getCartList(data);
  } else if (res?.result?.code === 403) {
    //未登录 去登录
    window.location.href = `/h5/user/login?pageSource=${window.location.href}`;
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

// 处理商品数据
function processGoodsData(goodsList: any[]) {
  goodsList.forEach((goods: any) => {
    goods.selected = goods.skuList.every((obj: any) => obj.selected);
    goods.skuTotalQuantity = 0;
    goods.skuSelectedQuantity = 0;

    goods.skuList.forEach((sku: any) => {
      if (!sku.minIncreaseQuantity) {
        sku.minIncreaseQuantity = goods.minIncreaseQuantity;
      }
      goods.skuTotalQuantity += sku.buyQty;
      if (sku.selected) {
        goods.skuSelected = true;
        goods.skuSelectedQuantity += sku.buyQty;
      }
    });
  });
}

// Tab切换
function switchTab(tab: "direct" | "quotation") {
  pageData.activeTab = tab;

  if (tab === "direct") {
    pageData.currentCartData = pageData.onlineOrderCartTab;
  } else {
    pageData.currentCartData = pageData.goodsLookingCartTab;
  }

  // 更新兼容字段
  pageData.goodsList = pageData.currentCartData?.goodsList || [];
  pageData.stat = pageData.currentCartData?.stat || {};
  pageData.totalEstimateFreight =
    pageData.currentCartData?.totalEstimateFreight || null;
}

// 获取当前购物车数据
function getCurrentCartData() {
  return pageData.currentCartData;
}

// 全选 - 使用新的 updateSelectedAll 接口
async function onAllSelection(value: any, supportOnlineOrder?: boolean) {
  // 根据当前激活的tab确定supportOnlineOrder
  let isOnlineOrder = supportOnlineOrder;
  if (isOnlineOrder === undefined) {
    isOnlineOrder = pageData.activeTab === "direct";
  }

  const res: any = await useUpdateSelectedAll({
    supportOnlineOrder: isOnlineOrder,
    selected: value,
  });
  if (res?.result?.code === 200) {
    onGetCartByTab();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
// 商品选中
function onGoodsSelection(value: any, goods: any) {
  onUpdateCart({ selected: value, goodsId: goods.goodsId, padc: goods.padc });
}
// sku选中
function onSkuSelection(value: any, sku: any, goods: any) {
  onUpdateCart({ selected: value, skuId: sku.skuId, padc: sku.padc });
}
// sku数量修改
function onCartQtyUpdate(value: any, sku: any, goods: any) {
  onUpdateCart({
    quantity: value,
    selected: sku.selected,
    skuId: sku.skuId,
    padc: sku.padc,
  });
}
// 修改
async function onUpdateCart(params: any) {
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    onGetCartByTab();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
// 删除商品
async function onDeleteGoods(goods: any) {
  onRemoveCart({ goodsId: goods.goodsId, padc: goods.padc });
}
// 删除sku
async function onDeleteSku(sku: any, goods: any) {
  onRemoveCart({ skuId: sku.skuId, padc: sku.padc });
}
// 删除
async function onRemoveCart(params: any) {
  const res: any = await useRemoveCart(params);
  if (res?.result?.code === 200) {
    message.success(authStore.i18n("cm_find.deleteSuccessMessage"), {
      duration: 3000,
    });
    onGetCartByTab();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
function onGoFindSubmit(event: any) {
  (window as any)?.MyStat?.addPageEvent(
    "click_start_looking",
    `点击创建询盘按钮`,
    true
  ); // 埋点
  if (pageData.stat?.selectTotalSalePrice < 2000) {
    pageData.submitDialogVisible = true;
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_open",
      `未满2000美元对话框-打开`
    ); // 埋点
    return;
  }
  onConfirmSubmit(event);
}

async function onConfirmSubmit(event: any, from?: any) {
  pageData.submitLoading = true;
  if (from) {
    window?.MyStat?.addPageEvent(
      "less_then_2000usd_dialog_ingore",
      `未满2000美元对话框-忽略`
    ); // 埋点
  }
  window?.MyStat?.addPageEvent(
    "carrito_click_inquiry",
    `购物车列表进入询盘提交页`
  ); // 埋点

  const selectedSkuList = <any>[];
  pageData.goodsList?.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });
  if (!!(window as any)?.fbq) {
    (window as any)?.fbq("track", "InitiateCheckout", {
      currency: "USD",
      num_items: selectedSkuList?.length,
      contents: selectedSkuList,
    });
  }
  if (!!(window as any)?.ttq) {
    (window as any)?.ttq?.track("InitiateCheckout", {
      currency: "USD",
      value: pageData?.stat?.selectTotalSalePrice,
      content_type: "product",
      description: JSON.stringify(selectedSkuList),
    });
  }

  const res: any = await useGetInquiry({
    params: selectedSkuList,
    siteId: window.siteData.siteInfo.id,
  });
  if (res?.result?.code === 200) {
    const inquiryInfo = res?.data;
    await authStore.setInquiryInfo(inquiryInfo);
    const url = navigateUrl(`/h5/find/submit`, {}, event);
    window.location.replace(url);
  } else {
    window?.MyStat?.addPageEvent(
      "submit_start_looking_error",
      `进入询盘信息页错误：${res?.result?.message}`
    ); // 埋点
    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

async function onOpenSkuDialog(sku: any, goods: any) {
  const res: any = await useGoodsInfo({
    id: goods.goodsId,
    padc: sku.padc,
    deviceType: 1,
  });
  if (res?.result?.code === 200) {
    pageData.currentSku = sku;
    pageData.updatedSku = sku;
    pageData.currentGoods = res?.data;
    await onInitGoodsData();
    pageData.dialogVisible = true;
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
async function onInitGoodsData() {
  pageData.selectedSpec = {};
  pageData.currentSku.specItemList.forEach((spec: any) => {
    pageData.selectedSpec[spec.groupId] = spec.itemId;
  });
}
function onSpecUpdate(specId: string, itemId: string, item: any) {
  if (item.disabled) return;
  pageData.selectedSpec[specId] = itemId;
}
// 更新最后一组规格组的状态 最后一组规格会校验sku的库存以及下架状态 如果没有查到sku 默认下架
function updateSpecStatus() {
  // 将对象转成数组 方便处理
  let selectList = <any>[];
  for (const id in pageData.selectedSpec) {
    selectList.push(pageData.selectedSpec[id]);
  }
  // 这里需要将选中的最后一组的规格给移除掉 再去找匹配的sku
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  lastSpecList.items.forEach((item: any) => {
    if (selectList.includes(item.itemId)) {
      const existingIndex = selectList.indexOf(item.itemId);
      if (existingIndex !== -1) {
        selectList.splice(existingIndex, 1);
      }
    }
  });
  lastSpecList.items.forEach((t: any) => {
    t.disabled = true;
  });
  // 获取所有满足条件的SKU
  const matchingSkus = pageData.currentGoods.skuList.filter((sku: any) => {
    return selectList.every((specItemId: string) =>
      sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === specItemId
      )
    );
  });
  // 如果有满足条件的SKU，则更新规格的可选状态
  if (matchingSkus.length > 0) {
    matchingSkus.forEach((sku: any) => {
      let disabled = true;
      // 判断当前选中的sku是否是当前sku 如果是则还是可选的
      if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
        disabled = false;
      }
      // 如果库存为0 则不可选 如果cartQty选购的数量大于0 则是已加购的规格 也是不可选的
      if (sku.stockQty != 0 && sku.cartQty == 0) {
        disabled = false;
      }
      const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
      lastSpecList.items.forEach((item: any) => {
        if (item.itemId == matchingItemId) {
          item.disabled = disabled;
        }
      });
    });
  } else {
    lastSpecList.items.forEach((item: any) => {
      item.disabled = true;
    });
  }
  onFilterSku();
}
// 只有一个规格时 判断当前规格的sku有没有库存以及上下架
function updateOneSpecStatus() {
  const onlyOneSpecItems = pageData.currentGoods?.specList[0].items;
  onlyOneSpecItems.forEach((t: any) => {
    t.disabled = true;
    const matchingSkus = pageData.currentGoods?.skuList.filter((sku: any) => {
      return sku.specItems.some(
        (skuSpecItem: any) => skuSpecItem.itemId === t.itemId
      );
    });
    if (matchingSkus.length > 0) {
      matchingSkus.forEach((sku: any) => {
        let disabled = true;
        // 判断当前选中的sku是否是当前sku 如果是则还是可选的
        if (sku.id == pageData.currentSku?.skuId && sku.stockQty != 0) {
          disabled = false;
        }
        if (sku.stockQty != 0 && sku.cartQty == 0) {
          disabled = false;
        }
        const matchingItemId = sku.specItems[sku.specItems.length - 1].itemId;
        onlyOneSpecItems.forEach((item: any) => {
          if (item.itemId == matchingItemId) {
            item.disabled = disabled;
          }
        });
      });
    } else {
      onlyOneSpecItems.forEach((item: any) => {
        item.disabled = true;
      });
    }
  });
  onFilterSku();
}

// 筛选出sku
function onFilterSku() {
  const lastSpecList =
    pageData.currentGoods.specList[pageData.currentGoods.specList.length - 1];
  // 选中的sku是下架或者没有库存的情况下 将最后一组规格里选中规格的取消
  for (const id in pageData.selectedSpec) {
    lastSpecList.items.forEach((item: any) => {
      if (item.itemId == pageData.selectedSpec[id] && item.disabled) {
        delete pageData.selectedSpec[id];
      }
    });
  }
  const skus = pageData.currentGoods.skuList.filter((sku: any) => {
    return (
      Object.entries(pageData.selectedSpec).every(([specId, itemId]) => {
        const matchingSpec = sku.specItems.find(
          (spec: any) => spec.specId === specId
        );
        if (!matchingSpec || matchingSpec.itemId !== itemId) {
          return false;
        }
        return true;
      }) && Object.keys(pageData.selectedSpec).length === sku.specItems.length
    );
  });
  pageData.updatedSku = skus.length > 0 ? skus[0] : null;
}
async function onConfirm() {
  const params = {
    selected: true,
    updatedSkuId: pageData.updatedSku.id,
    skuId: pageData.currentSku.skuId,
    goodsId: pageData.currentGoods.id,
    padc: pageData.currentSku.padc,
  };
  const res: any = await useUpdateCart(params);
  if (res?.result?.code === 200) {
    pageData.dialogVisible = false;
    onGetCartByTab();
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
function onCancel() {
  pageData.dialogVisible = false;
}

function onGoHome() {
  window.location.href = "/h5";
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  padding-bottom: 3.8rem;
}

:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
}
:deep(.n-checkbox__label) {
  width: 100%;
  padding-left: 0.12rem;
  padding-right: 0.12rem;
}
:deep(.divider .n-divider) {
  margin: 0;
}
.divider.n-divider:not(.n-divider--vertical) {
  margin: 0;
}
:deep(.divider .n-divider__line.n-divider__line--left) {
  display: none;
}
.spec-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 0.8rem;
  margin: 0.13rem 0.32rem 0.13rem 0;
  padding: 0 0.35rem;
  min-width: 0.8rem;
  cursor: pointer;
  color: #797979;
  border-radius: 0.1rem;
  border: 0.02rem solid #d7d7d7;
}
.disabled-btn {
  background: #d7d7d7;
  cursor: not-allowed;
  border: none;
}
.current-btn {
  color: #e50113;
  cursor: pointer;
  border: none;
  background: rgba(229, 1, 19, 0.18);
  .btn-tick {
    opacity: 1;
  }
}
.btn-tick {
  position: absolute;
  right: -0.2rem;
  bottom: 0;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.btn-tick::before {
  content: "";
  position: absolute;
  top: 0.1rem;
  right: 0.04rem;
  bottom: 0.02rem;
  left: 0.02rem;
  background-color: #e50113;
  z-index: -1;
  border-radius: 0.16rem 0 0.1rem 0;
}
:deep(.n-drawer-header) {
  padding-top: 0 !important;
  padding-left: 0.2rem !important;
  padding-right: 0.2rem !important;
  border: none !important;
}
:deep(.n-drawer-footer) {
  border: none !important;
}
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 0.2rem !important;
}
:deep(.n-card__content) {
  color: #000;
  padding: 0.48rem 0 !important;
  text-align: center;
}

.border-btn {
  border: 0.02rem solid #c7c7c7;
  &:hover {
    background: #e50113;
    color: #fff;
    border: none;
  }
}
.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
:deep(.country-delivery) {
  display: none;
}
:deep(.country-code) {
  font-size: 0.28rem;
  line-height: 0.28rem;
}
</style>
